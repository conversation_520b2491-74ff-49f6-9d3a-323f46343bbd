import math
import sympy as sp
import numpy as np
import random

# 定义符号变量t（用于后续方程求解）
t = sp.Symbol('t')

# 常数定义
g = 9.8
Vm = 300
Vn_min = 70
Vn_max = 140
r = 7

# 位置常数
cicle_up = (0, 200, 10)
cicle_down = (0, 200, 0)
bomb_org = (20000, 0, 2000)

# 计算导弹速度分量（基于初始位置到目标的方向）
distance = math.sqrt((20000-0)**2 + (2000-5)**2)
Vmx = Vm * 20000 / distance
Vmz = Vm * (2000-5) / distance

# 随时间变化的位置表达式（含符号变量t）
fire_bomb = (20000 - Vmx*t, 0, 2000 - Vmz*t)
cloud_fly = (17800 - (1.5+3.6)*120, 0, 1800 - 0.5*g*3.6*3.6)     

cloud_down = (17188, 0, 1800 - 0.5*g*3.6*3.6 - 3*(t-5.1))

# 蒙特卡洛算法生成圆内随机点
def generate_random_points_in_circle(center, radius, num_points):
    """在指定圆内生成随机点"""
    points = []
    for _ in range(num_points):
        # 生成圆内随机点
        angle = random.uniform(0, 2 * math.pi)
        r_random = radius * math.sqrt(random.uniform(0, 1))
        x = center[0] + r_random * math.cos(angle)
        y = center[1] + r_random * math.sin(angle)
        z = center[2]
        points.append((x, y, z))
    return points

def check_occlusion_at_time_with_custom_cloud(t_val, sample_points_up, sample_points_down, cloud_start_pos, cloud_start_time):
    """检查自定义云团位置在时间t_val时是否发生遮蔽"""
    # 只有t >= cloud_start_time时云团才出现
    if t_val > cloud_start_time:
        return False

    # 计算云团位置：x、y不变，z随时间下移
    cloud_pos = (cloud_start_pos[0], cloud_start_pos[1], cloud_start_pos[2] - 3*(t_val - cloud_start_time))

    # 计算导弹位置
    missile_pos = (20000 - Vmx * t_val, 0, 2000 - Vmz * t_val)
    if(cloud_pos[0] > missile_pos[0]):
        return False
    # 检查导弹到上圆点的连线
    for point_up in sample_points_up:
        # 向量从导弹到上圆点
        line_vec = np.array([point_up[0] - missile_pos[0],
                           point_up[1] - missile_pos[1],
                           point_up[2] - missile_pos[2]])

        # 向量从导弹到云团
        cloud_vec = np.array([cloud_pos[0] - missile_pos[0],
                            cloud_pos[1] - missile_pos[1],
                            cloud_pos[2] - missile_pos[2]])

        # 计算云团中心到直线的距离
        if np.linalg.norm(line_vec) > 0:
            cross_product = np.cross(cloud_vec, line_vec)
            distance = np.linalg.norm(cross_product) / np.linalg.norm(line_vec)

            # 如果有任何一条连线距离大于等于r，说明没有完全遮蔽，直接返回False
            if distance >= r:
                return False

    # 检查导弹到下圆点的连线
    for point_down in sample_points_down:
        # 向量从导弹到下圆点
        line_vec = np.array([point_down[0] - missile_pos[0],
                           point_down[1] - missile_pos[1],
                           point_down[2] - missile_pos[2]])

        # 向量从导弹到云团
        cloud_vec = np.array([cloud_pos[0] - missile_pos[0],
                            cloud_pos[1] - missile_pos[1],
                            cloud_pos[2] - missile_pos[2]])

        # 计算云团中心到直线的距离
        if np.linalg.norm(line_vec) > 0:
            cross_product = np.cross(cloud_vec, line_vec)
            distance = np.linalg.norm(cross_product) / np.linalg.norm(line_vec)

            # 如果有任何一条连线距离大于等于r，说明没有完全遮蔽，直接返回False
            if distance >= r:
                return False

    # 如果所有连线的距离都小于r，说明完全遮蔽
    return True

def check_occlusion_at_time(t_val, sample_points_up, sample_points_down):
    """检查在时间t_val时是否发生遮蔽（使用第一问的固定云团位置）"""
    # 只有t>5.1时云团才出现，才可能发生遮蔽
    if t_val <= 5.1:
        return False

    # 计算云团下降位置（第一问的固定位置）
    cloud_pos = (17188, 0, 1800 - 0.5*g*3.6*3.6 - 3*(t_val-5.1))

    # 计算导弹位置
    missile_pos = (20000 - Vmx * t_val, 0, 2000 - Vmz * t_val)
    if cloud_pos[0] > missile_pos[0]:
        return False

    # 检查导弹到上圆点的连线
    for point_up in sample_points_up:
        line_vec = np.array([point_up[0] - missile_pos[0],
                           point_up[1] - missile_pos[1],
                           point_up[2] - missile_pos[2]])

        cloud_vec = np.array([cloud_pos[0] - missile_pos[0],
                            cloud_pos[1] - missile_pos[1],
                            cloud_pos[2] - missile_pos[2]])

        if np.linalg.norm(line_vec) > 0:
            cross_product = np.cross(cloud_vec, line_vec)
            distance = np.linalg.norm(cross_product) / np.linalg.norm(line_vec)

            if distance >= r:
                return False

    # 检查导弹到下圆点的连线
    for point_down in sample_points_down:
        line_vec = np.array([point_down[0] - missile_pos[0],
                           point_down[1] - missile_pos[1],
                           point_down[2] - missile_pos[2]])

        cloud_vec = np.array([cloud_pos[0] - missile_pos[0],
                            cloud_pos[1] - missile_pos[1],
                            cloud_pos[2] - missile_pos[2]])

        if np.linalg.norm(line_vec) > 0:
            cross_product = np.cross(cloud_vec, line_vec)
            distance = np.linalg.norm(cross_product) / np.linalg.norm(line_vec)

            if distance >= r:
                return False

    return True

def find_occlusion_times(time_start=0, time_end=15, time_step=0.01, num_sample_points=500):
    """寻找遮蔽开始和结束时间"""
    # 生成上圆和下圆内的随机采样点
    sample_points_up = generate_random_points_in_circle(cicle_up, r, num_sample_points)
    sample_points_down = generate_random_points_in_circle(cicle_down, r, num_sample_points)

    start_time = None
    end_time = None
    was_occluded = False

    # 遍历时间点
    current_time = time_start

    while current_time <= time_end:
        is_occluded = check_occlusion_at_time(current_time, sample_points_up, sample_points_down)

        if is_occluded and not was_occluded:
            # 刚开始遮蔽
            start_time = current_time
            print(f"开始遮蔽时间: t_start = {current_time:.2f}")
        elif not is_occluded and was_occluded:
            # 刚结束遮蔽
            end_time = current_time
            print(f"结束遮蔽时间: t_end = {current_time:.2f}")
            break

        was_occluded = is_occluded
        current_time += time_step

    return start_time, end_time

def find_occlusion_duration_with_custom_cloud(cloud_start_pos, cloud_start_time, time_end=15, time_step=0.01, num_sample_points=500):
    """计算自定义云团位置的遮蔽持续时间"""
    sample_points_up = generate_random_points_in_circle(cicle_up, r, num_sample_points)
    sample_points_down = generate_random_points_in_circle(cicle_down, r, num_sample_points)

    start_time = None
    end_time = None
    was_occluded = False

    current_time = cloud_start_time

    while current_time <= time_end:
        is_occluded = check_occlusion_at_time_with_custom_cloud(current_time, sample_points_up, sample_points_down, cloud_start_pos, cloud_start_time)

        if is_occluded and not was_occluded:
            start_time = current_time
        elif not is_occluded and was_occluded:
            end_time = current_time
            break

        was_occluded = is_occluded
        current_time += time_step

    if start_time is not None and end_time is not None:
        return end_time - start_time
    else:
        return 0

def find_optimal_cloud_position():
    """遍历寻找能产生最长遮蔽时间的云团起始位置"""
    # 搜索范围定义
    x_min = 15857.00957220978  # 相遇点x坐标
    x_max = 17000
    y_min = 0
    y_max = 50
    z_min = 1501  # 相遇点z坐标
    z_max = 1700



    print(f"搜索范围:")
    print(f"x: {x_min:.1f} - {x_max:.1f}")
    print(f"y: {y_min:.1f} - {y_max:.1f}")
    print(f"z: {z_min:.1f} - {z_max:.1f}")
    print(f"时间: 0 - 15秒")

    max_duration = 0
    best_position = None
    best_start_time = None
    total_tests = 0

    print("\n开始遍历搜索最优云团位置...")

    # 在指定范围内遍历
    x_points = 11      # x方向点数
    y_points = 9       # y方向点数
    z_points = 50     # z方向点数 (对应500个步长)
    t_points = 11      # 时间点数

    x_step = (x_max - x_min) / (x_points - 1) if x_points > 1 else 0
    y_step = (y_max - y_min) / (y_points - 1) if y_points > 1 else 0
    z_step = (z_max - z_min) / (z_points - 1) if z_points > 1 else 0
    t_step = 15 / (t_points - 1) if t_points > 1 else 0

    for i in range(x_points):  # x方向
        test_x = x_min + i * x_step
        for j in range(y_points):   # y方向
            test_y = y_min + j * y_step
            for k in range(z_points):  # z方向
                test_z = z_min + k * z_step
                for l in range(t_points):  # 时间
                    test_time = l * t_step

                    test_pos = (test_x, test_y, test_z)
                    total_tests += 1

                    duration = find_occlusion_duration_with_custom_cloud(test_pos, test_time)

                    if duration > max_duration:
                        max_duration = duration
                        best_position = test_pos
                        best_start_time = test_time
                        print(f"找到更长遮蔽时间: {duration:.3f}秒, 位置: ({test_pos[0]:.1f}, {test_pos[1]:.1f}, {test_pos[2]:.1f}), 时间: {test_time:.1f}")

                    # 每1000次测试显示进度
                    if total_tests % 1000 == 0:
                        print(f"已测试 {total_tests} 个位置，当前最长遮蔽时间: {max_duration:.3f}秒")

    print(f"\n总共测试了 {total_tests} 个位置")
    return best_position, best_start_time, max_duration

# 运行第二问优化搜索
print("第二问：寻找最优云团位置")
best_pos, best_time, max_dur = find_optimal_cloud_position()

if best_pos is not None and max_dur > 0:
    print(f"\n最优结果:")
    print(f"云团起始位置: x={best_pos[0]:.3f}, y={best_pos[1]:.3f}, z={best_pos[2]:.3f}")
    print(f"云团出现时间: {best_time:.3f}秒")
    print(f"最长遮蔽时间: {max_dur:.3f}秒")
else:
    print("未找到有效的遮蔽位置")
