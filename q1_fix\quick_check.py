import math

# 常数定义
g = 9.8
Vm = 300

# 计算导弹速度分量
distance = math.sqrt((20000-0)**2 + (2000-5)**2)
Vmx = Vm * 20000 / distance
Vmz = Vm * (2000-5) / distance

print(f"导弹速度分量: Vmx={Vmx:.3f}, Vmz={Vmz:.3f}")

# 你之前的最优解
cloud_start_pos = (16685.6, 25.9, 1677.3)
cloud_start_time = 0.0

print(f"验证位置: {cloud_start_pos}")
print(f"验证时间: {cloud_start_time}")

# 检查关键时间点
print("\n关键分析:")
print("时间\t云团x\t导弹x\t云团z\t导弹z\t相对位置")
print("-" * 60)

for t in [0, 1, 2, 3, 4, 5, 10, 15]:
    # 云团位置
    cloud_x = cloud_start_pos[0]
    cloud_z = cloud_start_pos[2] - 3 * (t - cloud_start_time)
    
    # 导弹位置  
    missile_x = 20000 - Vmx * t
    missile_z = 2000 - Vmz * t
    
    # 相对位置判断
    if cloud_x > missile_x:
        relative = "云团在后"
    else:
        relative = "云团在前"
    
    print(f"{t}\t{cloud_x:.1f}\t{missile_x:.1f}\t{cloud_z:.1f}\t{missile_z:.1f}\t{relative}")

print("\n结论:")
print("如果云团一直在导弹后方，那么不可能产生遮蔽效果")
print("这可能解释了为什么实际遮蔽时间很短")
