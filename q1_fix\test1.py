import math
import sympy as sp
import numpy as np
import random

# 定义符号变量t（用于后续方程求解）
t = sp.Symbol('t')

# 常数定义
g = 9.8
Vm = 300
Vn_min = 70
Vn_max = 140
r = 7

# 位置常数
cicle_up = (0, 200, 0)
cicle_down = (0, 200, 10)
bomb_org = (20000, 0, 2000)

# 计算导弹速度分量（基于初始位置到目标的方向）
distance = math.sqrt((20000-0)**2 + (2000-5)**2)
Vmx = Vm * 20000 / distance
Vmz = Vm * (2000-5) / distance

# 随时间变化的位置表达式（含符号变量t）
fire_bomb = (20000 - Vmx*t, 0, 2000 - Vmz*t)
cloud_fly = (17800 - (1.5+3.6)*120, 0, 1800 - 0.5*g*3.6)
cloud_down = (17188, 0, 1800 - 0.5*g*3.6*3.6 - 3*(t-5.1))

# 蒙特卡洛算法生成圆内随机点
def generate_random_points_in_circle(center, radius, num_points):
    """在指定圆内生成随机点"""
    points = []
    for _ in range(num_points):
        # 生成圆内随机点
        angle = random.uniform(0, 2 * math.pi)
        r_random = radius * math.sqrt(random.uniform(0, 1))
        x = center[0] + r_random * math.cos(angle)
        y = center[1] + r_random * math.sin(angle)
        z = center[2]
        points.append((x, y, z))
    return points

def check_occlusion_at_time(t_val, sample_points_up, sample_points_down):
    """检查在时间t_val时是否发生遮蔽"""
    # 计算导弹在时间t_val的位置
    missile_pos = (
        20000 - Vmx * t_val,
        0,
        2000 - Vmz * t_val
    )

    # 检查导弹是否在上圆和下圆之间的柱体内
    for point_up in sample_points_up:
        for point_down in sample_points_down:
            # 计算导弹到上下圆点连线的距离
            # 向量从上圆点到下圆点
            line_vec = (point_down[0] - point_up[0],
                       point_down[1] - point_up[1],
                       point_down[2] - point_up[2])

            # 向量从上圆点到导弹
            missile_vec = (missile_pos[0] - point_up[0],
                          missile_pos[1] - point_up[1],
                          missile_pos[2] - point_up[2])

            # 计算点到直线的距离
            if np.linalg.norm(line_vec) > 0:
                cross_product = np.cross(missile_vec, line_vec)
                distance = np.linalg.norm(cross_product) / np.linalg.norm(line_vec)

                # 如果距离小于阈值，认为被遮蔽
                if distance < 1.0:  # 可调整的阈值
                    return True
    return False

def find_occlusion_times(time_start=0, time_end=20, time_step=0.1, num_sample_points=50):
    """寻找遮蔽开始和结束时间"""
    # 生成上圆和下圆内的随机采样点
    sample_points_up = generate_random_points_in_circle(cicle_up, r, num_sample_points)
    sample_points_down = generate_random_points_in_circle(cicle_down, r, num_sample_points)

    start_time = None
    end_time = None
    was_occluded = False

    # 遍历时间点
    current_time = time_start
    while current_time <= time_end:
        is_occluded = check_occlusion_at_time(current_time, sample_points_up, sample_points_down)

        if is_occluded and not was_occluded:
            # 刚开始遮蔽
            start_time = current_time
            print(f"开始遮蔽时间: t_start = {current_time:.2f}")
        elif not is_occluded and was_occluded:
            # 刚结束遮蔽
            end_time = current_time
            print(f"结束遮蔽时间: t_end = {current_time:.2f}")
            break

        was_occluded = is_occluded
        current_time += time_step

    return start_time, end_time

# 运行蒙特卡洛模拟
print("开始蒙特卡洛模拟...")
t_start, t_end = find_occlusion_times()

if t_start is not None and t_end is not None:
    print(f"遮蔽持续时间: {t_end - t_start:.2f} 秒")
else:
    print("未检测到完整的遮蔽过程")
