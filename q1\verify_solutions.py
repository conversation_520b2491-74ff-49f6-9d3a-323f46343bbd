import numpy as np

def verify_solution(t_val, x_val, y_val, z_val, point_name):
    """验证解的正确性"""
    print(f"=== 验证 {point_name} 的解 t = {t_val:.10f} ===")
    
    # 计算向量
    MC = np.array([x_val - 20000 + 298.5185*t_val, y_val, z_val - 2000 + 29.776*t_val])
    MF = np.array([298.5185*t_val - 2812, 0, 26.776*t_val - 248.204])
    
    print(f"MC = [{MC[0]:.6f}, {MC[1]:.6f}, {MC[2]:.6f}]")
    print(f"MF = [{MF[0]:.6f}, {MF[1]:.6f}, {MF[2]:.6f}]")
    
    # 计算模长
    abs_MC = np.linalg.norm(MC)
    abs_MF = np.linalg.norm(MF)
    
    print(f"|MC| = {abs_MC:.6f}")
    print(f"|MF| = {abs_MF:.6f}")
    
    # 检查约束条件
    constraint_satisfied = abs_MF >= 10
    print(f"约束条件 |MF| ≥ 10: {'✓' if constraint_satisfied else '✗'}")
    
    # 计算点积
    dot_MF_MC = np.dot(MF, MC)
    print(f"MF·MC = {dot_MF_MC:.6f}")
    
    # 计算等式左边
    if abs_MF >= 10:
        left_side = np.sqrt(1 - 100/abs_MF)
        print(f"左边: sqrt(1 - 100/|MF|) = {left_side:.10f}")
    else:
        print("左边: 不满足约束条件，无法计算")
        return False
    
    # 计算等式右边
    right_side = dot_MF_MC / (abs_MF * abs_MC)
    print(f"右边: (MF·MC)/(|MF|*|MC|) = {right_side:.10f}")
    
    # 计算差值
    difference = abs(left_side - right_side)
    print(f"差值: |左边 - 右边| = {difference:.2e}")
    
    # 判断是否为有效解
    is_valid = difference < 1e-8
    print(f"解的有效性: {'✓ 有效' if is_valid else '✗ 无效'}")
    
    return is_valid

def calculate_physical_meaning(t_val, x_val, y_val, z_val, point_name):
    """计算解的物理意义"""
    print(f"\n--- {point_name} 的物理意义 (t = {t_val:.6f}) ---")
    
    # 无人机位置 (假设从某个初始位置开始)
    # 这里需要根据具体的物理模型来计算
    
    # 导弹位置 (假设以恒定速度飞行)
    # M1 初始位置: (20000, 0, 2000), 速度: 300 m/s, 方向指向假目标(0,0,0)
    missile_initial = np.array([20000, 0, 2000])
    target_pos = np.array([0, 0, 0])
    missile_direction = (target_pos - missile_initial) / np.linalg.norm(target_pos - missile_initial)
    missile_speed = 300  # m/s
    
    missile_pos_at_t = missile_initial + missile_speed * t_val * missile_direction
    
    print(f"时刻 t = {t_val:.3f}s 时:")
    print(f"导弹位置: [{missile_pos_at_t[0]:.1f}, {missile_pos_at_t[1]:.1f}, {missile_pos_at_t[2]:.1f}]")
    
    # 计算导弹到目标的距离
    distance_to_target = np.linalg.norm(missile_pos_at_t - target_pos)
    print(f"导弹距离目标: {distance_to_target:.1f} m")
    
    # 计算剩余飞行时间
    remaining_time = distance_to_target / missile_speed
    print(f"导弹剩余飞行时间: {remaining_time:.3f} s")
    
    # 烟幕干扰弹的位置就是测试点
    smoke_pos = np.array([x_val, y_val, z_val])
    print(f"烟幕干扰弹位置: [{smoke_pos[0]:.1f}, {smoke_pos[1]:.1f}, {smoke_pos[2]:.1f}]")
    
    # 计算烟幕到导弹的距离
    distance_smoke_to_missile = np.linalg.norm(smoke_pos - missile_pos_at_t)
    print(f"烟幕到导弹距离: {distance_smoke_to_missile:.1f} m")

if __name__ == "__main__":
    print("=== 解的验证与分析 ===\n")
    
    # 解1: 点(0, 193, 0), t = 76.13067904
    t1 = 76.13067903651691
    valid1 = verify_solution(t1, 0, 193, 0, "点(0, 193, 0)")
    
    print("\n" + "="*60 + "\n")
    
    # 解2: 点(0, 207, 10), t = 76.91716743
    t2 = 76.9171674260127
    valid2 = verify_solution(t2, 0, 207, 10, "点(0, 207, 10)")
    
    print("\n" + "="*60)
    
    # 物理意义分析
    if valid1:
        calculate_physical_meaning(t1, 0, 193, 0, "点(0, 193, 0)")
    
    print("\n" + "-"*40)
    
    if valid2:
        calculate_physical_meaning(t2, 0, 207, 10, "点(0, 207, 10)")
    
    print("\n" + "="*60)
    print("总结:")
    print(f"点(0, 193, 0) 的解: t = {t1:.10f} {'(有效)' if valid1 else '(无效)'}")
    print(f"点(0, 207, 10) 的解: t = {t2:.10f} {'(有效)' if valid2 else '(无效)'}")
    
    if valid1 and valid2:
        time_diff = abs(t2 - t1)
        print(f"两个解的时间差: {time_diff:.6f} 秒")
        print("这表明不同位置的烟幕干扰弹需要在略微不同的时间起爆以达到最佳干扰效果")
