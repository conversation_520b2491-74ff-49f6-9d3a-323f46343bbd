import numpy as np
from scipy.optimize import fsolve, brentq
import matplotlib.pyplot as plt

def equation1(t):
    """
    第一个方程: 点 (0, 193, 0)
    左边: sqrt(1 - 100/|MF|)
    右边: (MF·MC)/(|MF|*|MC|)
    """
    # |MF|
    abs_MF = np.sqrt((26.776*t - 248.204)**2 + (298.5185*t - 2812)**2)
    
    # 检查约束条件
    if abs_MF < 10:
        return float('inf')
    
    # 左边
    left = np.sqrt(1 - 100/abs_MF)
    
    # 右边分子
    numerator = (26.776*t - 248.204)*(29.776*t - 2000) + (298.5185*t - 20000)*(298.5185*t - 2812)
    
    # 右边分母
    denominator = abs_MF * np.sqrt((29.776*t - 2000)**2 + (298.5185*t - 20000)**2 + 37249)
    
    # 右边
    right = numerator / denominator
    
    return left - right

def equation2(t):
    """
    第二个方程: 点 (0, 207, 10)
    左边: sqrt(1 - 100/|MF|)
    右边: (MF·MC)/(|MF|*|MC|)
    """
    # |MF|
    abs_MF = np.sqrt((26.776*t - 248.204)**2 + (298.5185*t - 2812)**2)
    
    # 检查约束条件
    if abs_MF < 10:
        return float('inf')
    
    # 左边
    left = np.sqrt(1 - 100/abs_MF)
    
    # 右边分子
    numerator = (26.776*t - 248.204)*(29.776*t - 1990) + (298.5185*t - 20000)*(298.5185*t - 2812)
    
    # 右边分母
    denominator = abs_MF * np.sqrt((29.776*t - 1990)**2 + (298.5185*t - 20000)**2 + 42849)
    
    # 右边
    right = numerator / denominator
    
    return left - right

def find_constraint_range():
    """找到约束条件 |MF| >= 10 的t范围"""
    # |MF|² = (26.776*t - 248.204)² + (298.5185*t - 2812)² >= 100
    
    a = 26.776**2 + 298.5185**2
    b = -2 * (26.776 * 248.204 + 298.5185 * 2812)
    c = 248.204**2 + 2812**2 - 100
    
    discriminant = b**2 - 4*a*c
    
    if discriminant < 0:
        return None
    
    t1 = (-b - np.sqrt(discriminant)) / (2*a)
    t2 = (-b + np.sqrt(discriminant)) / (2*a)
    
    return (min(t1, t2), max(t1, t2))

def solve_equation(equation_func, equation_name, search_ranges):
    """求解单个方程"""
    print(f"=== 求解{equation_name} ===")
    
    solutions = []
    
    for range_start, range_end in search_ranges:
        print(f"搜索范围: [{range_start:.3f}, {range_end:.3f}]")
        
        # 在范围内采样
        t_samples = np.linspace(range_start, range_end, 2000)
        f_values = []
        
        for t in t_samples:
            try:
                f_val = equation_func(t)
                if not np.isinf(f_val) and not np.isnan(f_val):
                    f_values.append(f_val)
                else:
                    f_values.append(np.inf)
            except:
                f_values.append(np.inf)
        
        f_values = np.array(f_values)
        valid_indices = ~np.isinf(f_values)
        
        if np.sum(valid_indices) < 2:
            print("  没有找到有效点")
            continue
        
        valid_t = t_samples[valid_indices]
        valid_f = f_values[valid_indices]
        
        # 寻找符号变化
        sign_changes = 0
        for i in range(len(valid_f) - 1):
            if valid_f[i] * valid_f[i+1] < 0:  # 符号变化
                try:
                    # 使用 Brent 方法求根
                    t_root = brentq(equation_func, valid_t[i], valid_t[i+1])
                    
                    # 验证解
                    residual = abs(equation_func(t_root))
                    if residual < 1e-10:
                        solutions.append(t_root)
                        sign_changes += 1
                        print(f"  找到解: t = {t_root:.8f} (残差: {residual:.2e})")
                        
                        # 验证约束条件
                        abs_MF = np.sqrt((26.776*t_root - 248.204)**2 + (298.5185*t_root - 2812)**2)
                        print(f"         |MF| = {abs_MF:.6f} (约束: ≥ 10)")
                
                except Exception as e:
                    pass
        
        print(f"  在此范围找到 {sign_changes} 个解")
    
    # 去重
    unique_solutions = []
    for sol in solutions:
        is_unique = True
        for existing in unique_solutions:
            if abs(sol - existing) < 1e-8:
                is_unique = False
                break
        if is_unique:
            unique_solutions.append(sol)
    
    print(f"\n{equation_name}的最终解: {len(unique_solutions)} 个")
    for i, sol in enumerate(sorted(unique_solutions)):
        print(f"t_{i+1} = {sol:.10f}")
    
    return sorted(unique_solutions)

def plot_equations():
    """绘制两个方程的图像"""
    constraint_range = find_constraint_range()
    if constraint_range is None:
        print("约束条件无解")
        return
    
    t_min, t_max = constraint_range
    
    # 扩展搜索范围
    t_plot1 = np.linspace(t_max, t_max + 30, 1000)
    t_plot2 = np.linspace(t_min - 30, t_min, 1000)
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 绘制方程1
    f1_values1 = [equation1(t) if not np.isinf(equation1(t)) else np.nan for t in t_plot1]
    f1_values2 = [equation1(t) if not np.isinf(equation1(t)) else np.nan for t in t_plot2]
    
    ax1.plot(t_plot1, f1_values1, 'b-', linewidth=2, label='方程1: 点(0,193,0)')
    ax1.plot(t_plot2, f1_values2, 'b-', linewidth=2)
    ax1.axhline(y=0, color='r', linestyle='--', alpha=0.7)
    ax1.set_xlabel('t')
    ax1.set_ylabel('F₁(t)')
    ax1.set_title('方程1: 点(0, 193, 0)')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 绘制方程2
    f2_values1 = [equation2(t) if not np.isinf(equation2(t)) else np.nan for t in t_plot1]
    f2_values2 = [equation2(t) if not np.isinf(equation2(t)) else np.nan for t in t_plot2]
    
    ax2.plot(t_plot1, f2_values1, 'g-', linewidth=2, label='方程2: 点(0,207,10)')
    ax2.plot(t_plot2, f2_values2, 'g-', linewidth=2)
    ax2.axhline(y=0, color='r', linestyle='--', alpha=0.7)
    ax2.set_xlabel('t')
    ax2.set_ylabel('F₂(t)')
    ax2.set_title('方程2: 点(0, 207, 10)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("=== 求解圆锥曲线方程 ===\n")
    
    # 找到约束条件范围
    constraint_range = find_constraint_range()
    if constraint_range is None:
        print("错误: 约束条件无解")
        exit()
    
    t_min, t_max = constraint_range
    print(f"约束条件 |MF| ≥ 10 对应的t范围:")
    print(f"t ∈ (-∞, {t_min:.6f}] ∪ [{t_max:.6f}, +∞)")
    print()
    
    # 定义非常大的搜索范围来寻找所有可能的解
    search_ranges = [
        (t_max, t_max + 50),      # 近距离右侧
        (t_max + 50, t_max + 200), # 中距离右侧
        (t_max + 200, t_max + 500), # 远距离右侧
        (t_min - 50, t_min),      # 近距离左侧
        (t_min - 200, t_min - 50), # 中距离左侧
        (t_min - 500, t_min - 200), # 远距离左侧
    ]

    # 先做快速检查，看看函数在不同t值处的表现
    print("扩大范围快速检查函数值:")
    test_points = [t_max + 5, t_max + 10, t_max + 20, t_max + 50, t_max + 100, t_max + 200, t_max + 300,
                   t_min - 5, t_min - 10, t_min - 20, t_min - 50, t_min - 100, t_min - 200, t_min - 300,
                   0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 30, 40, 50]

    print("方程1测试:")
    for tp in test_points:
        try:
            val = equation1(tp)
            if not np.isinf(val):
                print(f"  t = {tp:8.3f}: F₁(t) = {val:10.6f}")
        except:
            pass

    print("方程2测试:")
    for tp in test_points:
        try:
            val = equation2(tp)
            if not np.isinf(val):
                print(f"  t = {tp:8.3f}: F₂(t) = {val:10.6f}")
        except:
            pass
    print()
    
    # 求解方程1: 点(0, 193, 0)
    solutions1 = solve_equation(equation1, "方程1 [点(0, 193, 0)]", search_ranges)
    
    print("\n" + "="*60 + "\n")
    
    # 求解方程2: 点(0, 207, 10)
    solutions2 = solve_equation(equation2, "方程2 [点(0, 207, 10)]", search_ranges)
    
    print("\n" + "="*60)
    print("总结:")
    print(f"方程1 [点(0, 193, 0)] 的解: {solutions1}")
    print(f"方程2 [点(0, 207, 10)] 的解: {solutions2}")
    
