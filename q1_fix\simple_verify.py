import math
import numpy as np
import random

print("程序开始运行...")

# 常数定义
g = 9.8
Vm = 300
r = 7

# 计算导弹速度分量
distance = math.sqrt((20000-0)**2 + (2000-5)**2)
Vmx = Vm * 20000 / distance
Vmz = Vm * (2000-5) / distance

print(f"导弹速度分量: Vmx={Vmx:.3f}, Vmz={Vmz:.3f}")

# 你之前的最优解
cloud_start_pos = (16685.6, 25.9, 1677.3)
cloud_start_time = 0.0

print(f"验证位置: {cloud_start_pos}")
print(f"验证时间: {cloud_start_time}")

# 测试几个时间点
test_times = [0.0, 1.0, 2.0, 3.0, 4.0, 5.0]

for t in test_times:
    # 计算云团位置
    cloud_pos = (cloud_start_pos[0], cloud_start_pos[1], cloud_start_pos[2] - 3*(t - cloud_start_time))
    
    # 计算导弹位置
    missile_pos = (20000 - Vmx * t, 0, 2000 - Vmz * t)
    
    print(f"\n时间 t={t:.1f}秒:")
    print(f"  云团位置: ({cloud_pos[0]:.1f}, {cloud_pos[1]:.1f}, {cloud_pos[2]:.1f})")
    print(f"  导弹位置: ({missile_pos[0]:.1f}, {missile_pos[1]:.1f}, {missile_pos[2]:.1f})")
    
    # 检查基本条件
    if cloud_pos[0] > missile_pos[0]:
        print(f"  云团在导弹后方，不可能遮蔽")
    else:
        print(f"  云团在导弹前方，可能遮蔽")

print("\n基本验证完成")
