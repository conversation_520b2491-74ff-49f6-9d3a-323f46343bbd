import math
import numpy as np
import random

# 常数定义
g = 9.8
Vm = 300
r = 7

# 位置常数
cicle_up = (0, 200, 10)
cicle_down = (0, 200, 0)

# 计算导弹速度分量
distance = math.sqrt((20000-0)**2 + (2000-5)**2)
Vmx = Vm * 20000 / distance
Vmz = Vm * (2000-5) / distance

print(f"导弹速度分量: Vmx={Vmx:.3f}, Vmz={Vmz:.3f}")

# 蒙特卡洛算法生成圆内随机点
def generate_random_points_in_circle(center, radius, num_points):
    """在指定圆内生成随机点"""
    points = []
    for _ in range(num_points):
        angle = random.uniform(0, 2 * math.pi)
        r_random = radius * math.sqrt(random.uniform(0, 1))
        x = center[0] + r_random * math.cos(angle)
        y = center[1] + r_random * math.sin(angle)
        z = center[2]
        points.append((x, y, z))
    return points

def check_occlusion_at_time_with_custom_cloud(t_val, sample_points_up, sample_points_down, cloud_start_pos, cloud_start_time):
    """检查自定义云团位置在时间t_val时是否发生遮蔽"""
    # 只有t >= cloud_start_time时云团才出现
    if t_val < cloud_start_time:
        return False

    # 计算云团位置：x、y不变，z随时间下移
    cloud_pos = (cloud_start_pos[0], cloud_start_pos[1], cloud_start_pos[2] - 3*(t_val - cloud_start_time))

    # 计算导弹位置
    missile_pos = (20000 - Vmx * t_val, 0, 2000 - Vmz * t_val)
    
    # 如果云团在导弹后方，不可能遮蔽
    if cloud_pos[0] > missile_pos[0]:
        return False

    # 检查导弹到上圆点的连线
    for point_up in sample_points_up:
        line_vec = np.array([point_up[0] - missile_pos[0], 
                           point_up[1] - missile_pos[1], 
                           point_up[2] - missile_pos[2]])

        cloud_vec = np.array([cloud_pos[0] - missile_pos[0],
                            cloud_pos[1] - missile_pos[1],
                            cloud_pos[2] - missile_pos[2]])

        if np.linalg.norm(line_vec) > 0:
            cross_product = np.cross(cloud_vec, line_vec)
            distance = np.linalg.norm(cross_product) / np.linalg.norm(line_vec)

            if distance >= r:
                return False

    # 检查导弹到下圆点的连线
    for point_down in sample_points_down:
        line_vec = np.array([point_down[0] - missile_pos[0], 
                           point_down[1] - missile_pos[1], 
                           point_down[2] - missile_pos[2]])

        cloud_vec = np.array([cloud_pos[0] - missile_pos[0],
                            cloud_pos[1] - missile_pos[1],
                            cloud_pos[2] - missile_pos[2]])

        if np.linalg.norm(line_vec) > 0:
            cross_product = np.cross(cloud_vec, line_vec)
            distance = np.linalg.norm(cross_product) / np.linalg.norm(line_vec)

            if distance >= r:
                return False

    return True

def verify_occlusion_duration(cloud_start_pos, cloud_start_time, time_end=30, time_step=0.01, num_sample_points=500, seed=42):
    """验证特定云团位置的遮蔽持续时间"""
    print(f"\n=== 验证云团位置 ===")
    print(f"云团起始位置: ({cloud_start_pos[0]:.1f}, {cloud_start_pos[1]:.1f}, {cloud_start_pos[2]:.1f})")
    print(f"云团出现时间: {cloud_start_time:.1f}秒")
    print(f"采样点数: {num_sample_points}")
    print(f"随机种子: {seed}")
    
    # 设置固定随机种子
    random.seed(seed)
    np.random.seed(seed)
    
    sample_points_up = generate_random_points_in_circle(cicle_up, r, num_sample_points)
    sample_points_down = generate_random_points_in_circle(cicle_down, r, num_sample_points)
    
    start_time = None
    end_time = None
    was_occluded = False
    
    current_time = cloud_start_time
    occlusion_times = []
    
    print(f"\n开始详细检查遮蔽过程...")
    
    while current_time <= time_end:
        is_occluded = check_occlusion_at_time_with_custom_cloud(current_time, sample_points_up, sample_points_down, cloud_start_pos, cloud_start_time)
        
        if is_occluded:
            occlusion_times.append(current_time)
            
        if is_occluded and not was_occluded:
            start_time = current_time
            print(f"开始遮蔽时间: {current_time:.3f}秒")
            
            # 显示此时的位置信息
            cloud_pos = (cloud_start_pos[0], cloud_start_pos[1], cloud_start_pos[2] - 3*(current_time - cloud_start_time))
            missile_pos = (20000 - Vmx * current_time, 0, 2000 - Vmz * current_time)
            print(f"  云团位置: ({cloud_pos[0]:.1f}, {cloud_pos[1]:.1f}, {cloud_pos[2]:.1f})")
            print(f"  导弹位置: ({missile_pos[0]:.1f}, {missile_pos[1]:.1f}, {missile_pos[2]:.1f})")
            
        elif not is_occluded and was_occluded:
            end_time = current_time
            print(f"结束遮蔽时间: {current_time:.3f}秒")
            break
        
        was_occluded = is_occluded
        current_time += time_step
    
    if start_time is not None and end_time is not None:
        duration = end_time - start_time
        print(f"\n遮蔽持续时间: {duration:.3f}秒")
        print(f"遮蔽时间点总数: {len(occlusion_times)}")
        return duration
    else:
        print(f"\n未检测到完整的遮蔽过程")
        print(f"遮蔽时间点总数: {len(occlusion_times)}")
        return 0

# 验证你之前的最优解
print("开始验证之前的最优解:")
print("程序启动成功")
previous_best = (16685.6, 25.9, 1677.3)
previous_time = 0.0
print(f"要验证的位置: {previous_best}")
print(f"要验证的时间: {previous_time}")

# 只验证一次
print("开始验证...")
duration = verify_occlusion_duration(previous_best, previous_time, time_end=20, num_sample_points=100, seed=42)
print(f"\n最终结果: 遮蔽时间 = {duration:.3f}秒")
