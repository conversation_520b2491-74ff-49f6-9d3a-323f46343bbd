import math
import sympy as sp

# 常数定义
g = 9.8
Vm = 300
Vn_min = 70
Vn_max = 140
r = 7
# 计算导弹速度分量（基于初始位置到目标的方向）
distance = math.sqrt((20000-0)**2 + (2000-5)**2)
Vmx = Vm * 20000 / distance
Vmz = Vm * (2000-5) / distance

# 位置常数
cicle_up = (0, 200, 10)
cicle_down = (0, 200, 0)
bomb_org = (20000, 0, 2000)
FY1 = (17800, 0, 1800)

# 先确定云团爆开的可行域
# 当飞机全速延x轴飞行时，被bomb赶超时的位置
t_meeet = (-FY1[0] + bomb_org[0]) / (Vmx-Vn_max)
x_meet =  bomb_org[0]-Vmx*t_meeet
z_meet = FY1[2]-0.5*g*t_meeet**2
print("t_meeet:", t_meeet)
print("x_meet:", x_meet)
print("z_meet:", z_meet)
"""
t_meeet: 13.878503055644439
x_meet: 15857.00957220978
z_meet: 856.1970493788931
"""
position_able = []


